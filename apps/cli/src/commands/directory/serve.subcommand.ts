import { SubCommand, CommandRunner, Option } from 'nest-commander';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { configDotenv } from 'dotenv';
import chalk from 'chalk';
import ora from 'ora';
import { AgentHTTPModule } from '@packages/agent';
import { ConfigCheckService } from './config-check.service';

interface ServeOptions {
    port?: number;
    host?: string;
}

@SubCommand({
    name: 'serve',
    description: 'Start the localhost API server',
})
export class ServeSubCommand extends CommandRunner {
    private readonly logger = new Logger(ServeSubCommand.name);

    constructor(private readonly configCheck: ConfigCheckService) {
        super();
    }

    async run(passedParams: string[], options?: ServeOptions): Promise<void> {
        try {
            console.log(chalk.cyan.bold('\n🚀 Starting API Server\n'));

            // Check configuration first
            await this.configCheck.requireConfiguration();

            // Load environment variables
            configDotenv();

            const port = options?.port || parseInt(process.env.PORT || '3001', 10);
            const host = options?.host || process.env.HOST || 'localhost';

            console.log(chalk.cyan('--- Server Configuration ---'));
            console.log(chalk.gray('Host:'), chalk.white(host));
            console.log(chalk.gray('Port:'), chalk.white(port));
            console.log(chalk.gray('Environment:'), chalk.white(process.env.NODE_ENV || 'development'));

            const spinner = ora('Starting server...').start();

            try {
                // Create NestJS application
                const app = await NestFactory.create(AgentHTTPModule, {
                    logger: ['error', 'warn', 'log'],
                });

                // Configure global pipes
                app.useGlobalPipes(
                    new ValidationPipe({
                        whitelist: true,
                        transform: true,
                        forbidNonWhitelisted: true,
                    }),
                );

                // Enable CORS for development
                app.enableCors({
                    origin: true,
                    credentials: true,
                });

                // Start listening
                await app.listen(port, host);

                spinner.succeed('Server started successfully');

                console.log(chalk.green('\n✓ API Server is running!'));
                console.log(chalk.cyan('\n--- Server Information ---'));
                console.log(chalk.gray('Local URL:'), chalk.blue(`http://${host}:${port}`));
                console.log(chalk.gray('API Base:'), chalk.blue(`http://${host}:${port}/api`));

                console.log(chalk.cyan('\n--- Available Endpoints ---'));
                console.log(chalk.gray('Health Check:'), chalk.white(`GET /api/health`));
                console.log(chalk.gray('Create Directory:'), chalk.white(`POST /api/create`));
                console.log(chalk.gray('Update Directory:'), chalk.white(`POST /api/update/:slug`));
                console.log(chalk.gray('Submit Item:'), chalk.white(`POST /api/submit-item/:slug`));
                console.log(chalk.gray('Remove Item:'), chalk.white(`POST /api/remove-item/:slug`));
                console.log(chalk.gray('Regenerate Markdown:'), chalk.white(`POST /api/regenerate-markdown/:slug`));
                console.log(chalk.gray('Update Website:'), chalk.white(`POST /api/update-website/:slug`));
                console.log(chalk.gray('Delete Directory:'), chalk.white(`POST /api/delete/:slug`));

                console.log(chalk.yellow('\n--- Controls ---'));
                console.log(chalk.gray('Press'), chalk.white('Ctrl+C'), chalk.gray('to stop the server'));

                // Keep the process alive
                process.on('SIGINT', () => {
                    console.log(chalk.yellow('\n\n⚠ Shutting down server...'));
                    app.close().then(() => {
                        console.log(chalk.green('✓ Server stopped successfully'));
                        process.exit(0);
                    });
                });

                process.on('SIGTERM', () => {
                    console.log(chalk.yellow('\n\n⚠ Shutting down server...'));
                    app.close().then(() => {
                        console.log(chalk.green('✓ Server stopped successfully'));
                        process.exit(0);
                    });
                });

            } catch (error) {
                spinner.fail('Failed to start server');
                throw error;
            }

        } catch (error) {
            this.logger.error('Failed to start API server:', error);
            console.log(chalk.red('\n✗ Failed to start API server:'), error.message);
            
            if (error.code === 'EADDRINUSE') {
                console.log(chalk.yellow('\n💡 Tip: The port might already be in use.'));
                console.log(chalk.gray('Try using a different port with:'), chalk.cyan('--port <number>'));
            }
            
            process.exit(1);
        }
    }

    @Option({
        flags: '-p, --port <number>',
        description: 'Port to run the server on',
    })
    parsePort(val: string): number {
        const port = parseInt(val, 10);
        if (isNaN(port) || port < 1 || port > 65535) {
            throw new Error('Port must be a number between 1 and 65535');
        }
        return port;
    }

    @Option({
        flags: '-h, --host <string>',
        description: 'Host to bind the server to',
    })
    parseHost(val: string): string {
        if (!val || val.trim().length === 0) {
            throw new Error('Host cannot be empty');
        }
        return val.trim();
    }
}
