import { SubCommand, CommandRunner } from 'nest-commander';
import { Logger } from '@nestjs/common';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import {
    DirectoryRepository,
    AgentService
} from '@packages/agent';
import { DirectoryPromptService } from './directory-prompt.service';
import { ConfigCheckService } from './config-check.service';

@SubCommand({
    name: 'update',
    description: 'Update a directory and its GitHub repository',
})
export class UpdateSubCommand extends CommandRunner {
    private readonly logger = new Logger(UpdateSubCommand.name);

    constructor(
        private readonly directoryRepository: DirectoryRepository,
        private readonly directoryPrompt: DirectoryPromptService,
        private readonly configCheck: ConfigCheckService,
        private readonly dataGenerator: DataGeneratorService,
    ) {
        super();
    }

    async run(): Promise<void> {
        try {
            console.log(chalk.cyan.bold('\n🔄 Update Directory\n'));

            // Check configuration first
            await this.configCheck.requireConfiguration();

            // Select directory
            const selection = await this.directoryPrompt.promptDirectorySelection(this.directoryRepository);
            if (selection.cancelled || !selection.directory) {
                console.log(chalk.yellow('\n⚠ Operation cancelled.'));
                return;
            }

            const directory = selection.directory;
            console.log(chalk.green(`\n✓ Selected directory: ${directory.slug}`));

            // Prompt for update options
            const updateOptions = await this.promptUpdateOptions();

            // Show confirmation
            console.log(chalk.cyan('\n--- Update Summary ---'));
            console.log(chalk.gray('Directory:'), chalk.white(directory.slug));
            console.log(chalk.gray('Generation Method:'), chalk.white(updateOptions.generation_method));
            console.log(chalk.gray('Update with PR:'), chalk.white(updateOptions.update_with_pull_request ? 'Yes' : 'No'));

            const confirmed = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'proceed',
                    message: 'Proceed with the update?',
                    default: true,
                },
            ]);

            if (!confirmed.proceed) {
                console.log(chalk.yellow('\n⚠ Update cancelled.'));
                return;
            }

            // Perform update
            const spinner = ora('Updating directory...').start();

            try {
                // Get user and call the service method directly
                const user = await User.sessionMock();

                // Get last request data
                let lastRequestData = await this.dataGenerator.getLastRequestData(directory, user).catch(() => null);

                if (!lastRequestData) {
                    spinner.fail('No last request data found');
                    console.log(chalk.red('\n✗ No previous generation data found for this directory.'));
                    console.log(chalk.gray('You may need to run the initial generation first.'));
                    return;
                }

                // Merge with update options
                const mergedData = {
                    ...lastRequestData,
                    ...updateOptions
                };

                // Call the data generator service
                const generated = await this.dataGenerator.initialize(directory, user, mergedData);

                if (generated) {
                    spinner.succeed('Directory updated successfully');
                    console.log(chalk.green('\n✓ Update completed successfully!'));
                    console.log(chalk.gray('Directory:'), chalk.white(directory.slug));
                    console.log(chalk.gray('Generation Method:'), chalk.white(updateOptions.generation_method));
                    console.log(chalk.gray('Update with PR:'), chalk.white(updateOptions.update_with_pull_request ? 'Yes' : 'No'));
                } else {
                    spinner.fail('Update completed but no items were generated');
                    console.log(chalk.yellow('\n⚠ Update completed but no new items were generated.'));
                }

            } catch (error) {
                spinner.fail('Failed to update directory');
                throw error;
            }

        } catch (error) {
            this.logger.error('Failed to update directory:', error);
            console.log(chalk.red('\n✗ Failed to update directory:'), error.message);
        }
    }

    private async promptUpdateOptions() {
        console.log(chalk.cyan('\n--- Update Options ---'));

        const { generation_method } = await inquirer.prompt([
            {
                type: 'list',
                name: 'generation_method',
                message: 'Select generation method:',
                choices: [
                    {
                        name: 'Create/Update - Add new items and update existing ones',
                        value: 'create-update',
                        short: 'create-update',
                    },
                    {
                        name: 'Recreate - Replace all existing items',
                        value: 'recreate',
                        short: 'recreate',
                    },
                ],
                default: 'create-update',
            },
        ]);

        const { update_with_pull_request } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'update_with_pull_request',
                message: 'Create pull request for updates?',
                default: true,
            },
        ]);

        return {
            generation_method,
            update_with_pull_request,
        };
    }
}
