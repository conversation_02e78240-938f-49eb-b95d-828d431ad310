import { SubCommand, CommandRunner } from 'nest-commander';
import { Logger } from '@nestjs/common';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { 
    DirectoryRepository, 
    DataGeneratorService,
    MarkdownGeneratorService,
    WebsiteGeneratorService,
    User
} from '@packages/agent';
import { DirectoryPromptService } from './directory-prompt.service';
import { ConfigCheckService } from './config-check.service';

@SubCommand({
    name: 'delete',
    description: 'Delete a directory and its GitHub repositories',
})
export class DeleteSubCommand extends CommandRunner {
    private readonly logger = new Logger(DeleteSubCommand.name);

    constructor(
        private readonly directoryRepository: DirectoryRepository,
        private readonly directoryPrompt: DirectoryPromptService,
        private readonly configCheck: ConfigCheckService,
        private readonly dataGenerator: DataGeneratorService,
        private readonly markdownGenerator: MarkdownGeneratorService,
        private readonly websiteGenerator: WebsiteGeneratorService,
    ) {
        super();
    }

    async run(): Promise<void> {
        try {
            console.log(chalk.cyan.bold('\n🗑️  Delete Directory\n'));

            // Check configuration first
            await this.configCheck.requireConfiguration();

            // Select directory
            const selection = await this.directoryPrompt.promptDirectorySelection(this.directoryRepository);
            if (selection.cancelled || !selection.directory) {
                console.log(chalk.yellow('\n⚠ Operation cancelled.'));
                return;
            }

            const directory = selection.directory;
            console.log(chalk.green(`\n✓ Selected directory: ${directory.slug}`));

            // Prompt for deletion options
            const deleteOptions = await this.promptDeleteOptions();

            // Show what will be deleted
            console.log(chalk.cyan('\n--- Deletion Summary ---'));
            console.log(chalk.gray('Directory:'), chalk.white(directory.slug));
            console.log(chalk.gray('Owner:'), chalk.white(directory.owner));
            
            const repositoriesToDelete = [];
            if (deleteOptions.delete_data_repository) {
                repositoriesToDelete.push(`${directory.owner}/${directory.getDataRepo()}`);
            }
            if (deleteOptions.delete_markdown_repository) {
                repositoriesToDelete.push(`${directory.owner}/${directory.slug}`);
            }
            if (deleteOptions.delete_website_repository) {
                repositoriesToDelete.push(`${directory.owner}/${directory.getWebsiteRepo()}`);
            }

            if (repositoriesToDelete.length > 0) {
                console.log(chalk.gray('\nRepositories to delete:'));
                repositoriesToDelete.forEach(repo => {
                    console.log(chalk.red(`  • ${repo}`));
                });
            }

            if (deleteOptions.reason) {
                console.log(chalk.gray('\nReason:'), chalk.white(deleteOptions.reason));
            }

            console.log(chalk.red('\n⚠ WARNING: This action is IRREVERSIBLE!'));
            console.log(chalk.red('All data, repositories, and configurations will be permanently deleted.'));

            // Double confirmation
            const firstConfirm = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'proceed',
                    message: 'Are you absolutely sure you want to delete this directory?',
                    default: false,
                },
            ]);

            if (!firstConfirm.proceed) {
                console.log(chalk.yellow('\n⚠ Deletion cancelled.'));
                return;
            }

            // Type confirmation
            const typeConfirm = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'confirmation',
                    message: `Type "${directory.slug}" to confirm deletion:`,
                    validate: (input) => {
                        if (input !== directory.slug) {
                            return `You must type "${directory.slug}" exactly to confirm`;
                        }
                        return true;
                    },
                },
            ]);

            if (typeConfirm.confirmation !== directory.slug) {
                console.log(chalk.yellow('\n⚠ Deletion cancelled.'));
                return;
            }

            // Perform deletion
            const spinner = ora('Deleting directory and repositories...').start();

            try {
                // Get user and call the service methods directly
                const user = await User.sessionMock();
                const deletedRepositories: string[] = [];

                // Delete data repository if requested
                if (deleteOptions.delete_data_repository) {
                    try {
                        await this.dataGenerator.removeRepository(directory, user);
                        deletedRepositories.push(`${directory.owner}/${directory.getDataRepo()}`);
                    } catch (error) {
                        this.logger.error('Failed to delete data repository:', error);
                    }
                }

                // Delete markdown repository if requested
                if (deleteOptions.delete_markdown_repository) {
                    try {
                        await this.markdownGenerator.removeRepository(directory, user);
                        deletedRepositories.push(`${directory.owner}/${directory.slug}`);
                    } catch (error) {
                        this.logger.error('Failed to delete markdown repository:', error);
                    }
                }

                // Delete website repository if requested
                if (deleteOptions.delete_website_repository) {
                    try {
                        await this.websiteGenerator.removeRepository(directory, user);
                        deletedRepositories.push(`${directory.owner}/${directory.getWebsiteRepo()}`);
                    } catch (error) {
                        this.logger.error('Failed to delete website repository:', error);
                    }
                }

                // Remove directory from database
                await this.directoryRepository.delete(directory.id);
                
                spinner.succeed('Directory and repositories deleted successfully');

                console.log(chalk.green('\n✓ Directory deleted successfully!'));
                console.log(chalk.gray('Deleted directory:'), chalk.white(directory.slug));
                
                if (deletedRepositories.length > 0) {
                    console.log(chalk.gray('\nDeleted repositories:'));
                    deletedRepositories.forEach(repo => {
                        console.log(chalk.gray(`  • ${repo}`));
                    });
                }

            } catch (error) {
                spinner.fail('Failed to delete directory');
                throw error;
            }

        } catch (error) {
            this.logger.error('Failed to delete directory:', error);
            console.log(chalk.red('\n✗ Failed to delete directory:'), error.message);
        }
    }

    private async promptDeleteOptions() {
        console.log(chalk.cyan('\n--- Deletion Options ---'));

        const { delete_data_repository } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'delete_data_repository',
                message: 'Delete data repository?',
                default: true,
            },
        ]);

        const { delete_markdown_repository } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'delete_markdown_repository',
                message: 'Delete markdown repository?',
                default: true,
            },
        ]);

        const { delete_website_repository } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'delete_website_repository',
                message: 'Delete website repository?',
                default: true,
            },
        ]);

        const { reason } = await inquirer.prompt([
            {
                type: 'input',
                name: 'reason',
                message: 'Reason for deletion (optional):',
                validate: (input) => {
                    if (input && input.length > 500) {
                        return 'Reason must be less than 500 characters';
                    }
                    return true;
                },
            },
        ]);

        return {
            delete_data_repository,
            delete_markdown_repository,
            delete_website_repository,
            reason: reason.trim() || undefined,
        };
    }
}
