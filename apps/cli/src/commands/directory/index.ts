import { DirectoryCommand } from './directory.command';
import { CreateSubCommand } from './create.subcommand';
import { ListSubCommand } from './list.subcommand';
import { UpdateSubCommand } from './update.subcommand';
import { SubmitItemSubCommand } from './submit-item.subcommand';
import { RemoveItemSubCommand } from './remove-item.subcommand';
import { RegenerateMarkdownSubCommand } from './regenerate-markdown.subcommand';
import { UpdateWebsiteSubCommand } from './update-website.subcommand';
import { DeploySubCommand } from './deploy.subcommand';
import { DeleteSubCommand } from './delete.subcommand';
import { DirectoryPromptService } from './directory-prompt.service';
import { ConfigCheckService } from './config-check.service';

export const DirectoryCommands = [
    // Commands
    DirectoryCommand,
    CreateSubCommand,
    ListSubCommand,
    UpdateSubCommand,
    SubmitItemSubCommand,
    RemoveItemSubCommand,
    RegenerateMarkdownSubCommand,
    UpdateWebsiteSubCommand,
    DeploySubCommand,
    DeleteSubCommand,

    // Services
    DirectoryPromptService,
    ConfigCheckService,
];
